@tailwind base;
@tailwind components;
@tailwind utilities;
html{
  scroll-behavior: smooth;
}
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 0%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 0%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 0%;
    --primary: 0 0% 0%;
    --primary-foreground: 0 0% 100%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 0%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 0%;
    --destructive: 0 0% 0%;
    --destructive-foreground: 0 0% 100%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 0%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 0 0% 0%;
    --foreground: 0 0% 100%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 100%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 100%;
    --primary: 0 0% 100%;
    --primary-foreground: 0 0% 0%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 100%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 100%;
    --destructive: 0 0% 63.9%;
    --destructive-foreground: 0 0% 0%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    overflow-x: hidden;
    height: 100%;
    margin: 0;
    padding: 0;
  }
  html {
    height: 100%;
    overflow-y: hidden;
    overflow-x: hidden;
  }
}

/* Hide scrollbar */
::-webkit-scrollbar {
  display: none;
}

* {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* Dark mode enhancements */
.dark .shadow-md {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 2px 4px -1px rgba(0, 0, 0, 0.1);
}

/* Scroll indicator hover effect */
.fixed.bottom-0.left-0.w-full.h-2:hover {
  height: 4px;
  transition: height 0.2s ease;
}

/* Vertical text for sections */
.vertical-text {
  writing-mode: vertical-rl;
  text-orientation: mixed;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  letter-spacing: 0.5em;
}

/* For mobile references section */
@media (max-width: 768px) {
  .grid.md\\:grid-cols-5 > div {
    height: 300px;
  }
}

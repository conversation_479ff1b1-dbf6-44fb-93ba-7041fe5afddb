import type React from "react"
import "./globals.css"
import type { Metada<PERSON> } from "next"
import { Inter } from "next/font/google"
import { LanguageProvider } from "@/contexts/language-context"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Rokosz Media",
  description: "Expert Film and Visual Content Production with Innovative Marketing Solutions",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning className="dark text-[13px]">
      <head>
        <link rel="shortcut icon" href="/images/rokosz.png" type="image/x-icon" />
      </head>
      <body className={inter.className}>
        <LanguageProvider>{children}</LanguageProvider>
      </body>
    </html>
  )
}
